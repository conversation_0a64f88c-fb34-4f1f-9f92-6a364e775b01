{"rustc": 16591470773350601817, "features": "[\"alloc\", \"ansi\", \"default\", \"env-filter\", \"fmt\", \"matchers\", \"nu-ansi-term\", \"once_cell\", \"regex\", \"registry\", \"sharded-slab\", \"smallvec\", \"std\", \"thread_local\", \"tracing\", \"tracing-log\"]", "declared_features": "[\"alloc\", \"ansi\", \"chrono\", \"default\", \"env-filter\", \"fmt\", \"json\", \"local-time\", \"matchers\", \"nu-ansi-term\", \"once_cell\", \"parking_lot\", \"regex\", \"registry\", \"serde\", \"serde_json\", \"sharded-slab\", \"smallvec\", \"std\", \"thread_local\", \"time\", \"tracing\", \"tracing-log\", \"tracing-serde\", \"valuable\", \"valuable-serde\", \"valuable_crate\"]", "target": 4817557058868189149, "profile": 11202463608144111571, "path": 4518446411289648021, "deps": [[1009387600818341822, "matchers", false, 6100191519324041856], [1017461770342116999, "sharded_slab", false, 6349270959751820741], [1359731229228270592, "thread_local", false, 5589882495257340746], [3424551429995674438, "tracing_core", false, 10884133768396939547], [3666196340704888985, "smallvec", false, 11735701237199158941], [3722963349756955755, "once_cell", false, 12691404913753965951], [8606274917505247608, "tracing", false, 18191650935807564685], [8614575489689151157, "nu_ansi_term", false, 13876145443023619183], [9451456094439810778, "regex", false, 6740499756172902883], [10806489435541507125, "tracing_log", false, 15355072186626196830]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tracing-subscriber-6644d0c0e88b1071\\dep-lib-tracing_subscriber", "checksum": false}}], "rustflags": ["-L", "C:\\Users\\<USER>\\Desktop\\onnxruntime-win-x64-1.22.1\\lib", "-l", "onnxruntime"], "config": 2069994364910194474, "compile_kind": 0}