{"rustc": 16591470773350601817, "features": "[]", "declared_features": "[\"compression\", \"debug-embed\", \"deterministic-timestamps\", \"include-exclude\", \"interpolate-folder-path\", \"mime-guess\", \"shellexpand\"]", "target": 14492244735130954444, "profile": 2225463790103693989, "path": 18038911769081715924, "deps": [[3060637413840920116, "proc_macro2", false, 16579536504943866], [4974441333307933176, "syn", false, 10926099974306506772], [5409933923103361951, "rust_embed_utils", false, 6763808799669396164], [15622660310229662834, "walkdir", false, 2905221155058074648], [17990358020177143287, "quote", false, 18188863752954045910]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\rust-embed-impl-5328d8206e4c1c06\\dep-lib-rust_embed_impl", "checksum": false}}], "rustflags": ["-L", "C:\\Users\\<USER>\\Desktop\\onnxruntime-win-x64-1.22.1\\lib", "-l", "onnxruntime"], "config": 2069994364910194474, "compile_kind": 0}