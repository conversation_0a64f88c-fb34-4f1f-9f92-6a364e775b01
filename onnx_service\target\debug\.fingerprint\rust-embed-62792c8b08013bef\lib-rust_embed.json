{"rustc": 16591470773350601817, "features": "[]", "declared_features": "[\"actix\", \"actix-web\", \"axum\", \"axum-ex\", \"compression\", \"debug-embed\", \"deterministic-timestamps\", \"hex\", \"include-exclude\", \"include-flate\", \"interpolate-folder-path\", \"mime-guess\", \"mime_guess\", \"poem\", \"poem-ex\", \"rocket\", \"salvo\", \"salvo-ex\", \"tokio\", \"warp\", \"warp-ex\"]", "target": 3385222681681722461, "profile": 2241668132362809309, "path": 11352518296192317988, "deps": [[5409933923103361951, "rust_embed_utils", false, 16993568556141013977], [11693977163544003021, "rust_embed_impl", false, 11771802073551227042], [15622660310229662834, "walkdir", false, 17631311570909751810]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\rust-embed-62792c8b08013bef\\dep-lib-rust_embed", "checksum": false}}], "rustflags": ["-L", "C:\\Users\\<USER>\\Desktop\\onnxruntime-win-x64-1.22.1\\lib", "-l", "onnxruntime"], "config": 2069994364910194474, "compile_kind": 0}