{"rustc": 16591470773350601817, "features": "[\"cgemm\", \"std\"]", "declared_features": "[\"cgemm\", \"constconf\", \"default\", \"num_cpus\", \"once_cell\", \"std\", \"thread-tree\", \"threading\"]", "target": 7055067433712553826, "profile": 2241668132362809309, "path": 4862524456969268496, "deps": [[15709748443193639506, "rawpointer", false, 7504423604477523260], [15826188163127377936, "build_script_build", false, 7819019734697091717]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\matrixmultiply-c5a8199675eaa968\\dep-lib-matrixmultiply", "checksum": false}}], "rustflags": ["-L", "C:\\Users\\<USER>\\Desktop\\onnxruntime-win-x64-1.22.1\\lib", "-l", "onnxruntime"], "config": 2069994364910194474, "compile_kind": 0}