{"rustc": 16591470773350601817, "features": "[\"default\", \"log\", \"tracing\"]", "declared_features": "[\"__common\", \"balance\", \"buffer\", \"default\", \"discover\", \"filter\", \"full\", \"futures-core\", \"futures-util\", \"hdrhistogram\", \"hedge\", \"indexmap\", \"limit\", \"load\", \"load-shed\", \"log\", \"make\", \"pin-project\", \"pin-project-lite\", \"rand\", \"ready-cache\", \"reconnect\", \"retry\", \"slab\", \"spawn-ready\", \"steer\", \"timeout\", \"tokio\", \"tokio-stream\", \"tokio-util\", \"tracing\", \"util\"]", "target": 3486700084251681313, "profile": 2241668132362809309, "path": 14869122394325135932, "deps": [[784494742817713399, "tower_service", false, 15258583568542421950], [7712452662827335977, "tower_layer", false, 7421703447326545080], [8606274917505247608, "tracing", false, 18191650935807564685]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tower-7dc0a17ec054a718\\dep-lib-tower", "checksum": false}}], "rustflags": ["-L", "C:\\Users\\<USER>\\Desktop\\onnxruntime-win-x64-1.22.1\\lib", "-l", "onnxruntime"], "config": 2069994364910194474, "compile_kind": 0}