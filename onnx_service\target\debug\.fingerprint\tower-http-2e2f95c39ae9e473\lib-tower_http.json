{"rustc": 16591470773350601817, "features": "[\"cors\", \"default\", \"fs\", \"futures-util\", \"httpdate\", \"mime\", \"mime_guess\", \"percent-encoding\", \"set-status\", \"tokio\", \"tokio-util\", \"tracing\"]", "declared_features": "[\"add-extension\", \"async-compression\", \"auth\", \"base64\", \"catch-panic\", \"compression-br\", \"compression-deflate\", \"compression-full\", \"compression-gzip\", \"compression-zstd\", \"cors\", \"decompression-br\", \"decompression-deflate\", \"decompression-full\", \"decompression-gzip\", \"decompression-zstd\", \"default\", \"follow-redirect\", \"fs\", \"full\", \"futures-core\", \"futures-util\", \"httpdate\", \"iri-string\", \"limit\", \"map-request-body\", \"map-response-body\", \"metrics\", \"mime\", \"mime_guess\", \"normalize-path\", \"percent-encoding\", \"propagate-header\", \"redirect\", \"request-id\", \"sensitive-headers\", \"set-header\", \"set-status\", \"timeout\", \"tokio\", \"tokio-util\", \"tower\", \"trace\", \"tracing\", \"util\", \"uuid\", \"validate-request\"]", "target": 14850331575045365232, "profile": 2241668132362809309, "path": 7438040620691593739, "deps": [[40386456601120721, "percent_encoding", false, 13788422413574558869], [784494742817713399, "tower_service", false, 15258583568542421950], [1288403060204016458, "tokio_util", false, 194530546023888623], [1906322745568073236, "pin_project_lite", false, 10960016507308762011], [6304235478050270880, "httpdate", false, 3520337012376168781], [7712452662827335977, "tower_layer", false, 7421703447326545080], [7896293946984509699, "bitflags", false, 1948332885105997935], [8606274917505247608, "tracing", false, 18191650935807564685], [9010263965687315507, "http", false, 888770377160779222], [10229185211513642314, "mime", false, 16824344108290317185], [10629569228670356391, "futures_util", false, 15286345949843558659], [12393800526703971956, "tokio", false, 970316927202655419], [12475322156296016012, "http_range_header", false, 18089980184604186814], [14084095096285906100, "http_body", false, 3615934572663458495], [16066129441945555748, "bytes", false, 14526016353561065470], [16900715236047033623, "http_body_util", false, 5569380279369487524], [18071510856783138481, "mime_guess", false, 13746482764082197922]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tower-http-2e2f95c39ae9e473\\dep-lib-tower_http", "checksum": false}}], "rustflags": ["-L", "C:\\Users\\<USER>\\Desktop\\onnxruntime-win-x64-1.22.1\\lib", "-l", "onnxruntime"], "config": 2069994364910194474, "compile_kind": 0}