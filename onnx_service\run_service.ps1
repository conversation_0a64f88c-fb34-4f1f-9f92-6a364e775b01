# ONNX Service Startup Script with ONNX Runtime 1.22.1
# This script ensures the correct version of ONNX Runtime is used

Write-Host "🚀 Starting ONNX Service with ONNX Runtime 1.22.1..." -ForegroundColor Green
Write-Host ""

# 设置环境变量
$env:ORT_STRATEGY = "system"
$env:ORT_LIB_LOCATION = "C:\Users\<USER>\Desktop\onnxruntime-win-x64-1.22.1\lib"

# ONNX Runtime 路径
$onnxPath = "C:\Users\<USER>\Desktop\onnxruntime-win-x64-1.22.1\lib"

Write-Host "Environment variables set:" -ForegroundColor Yellow
Write-Host "  ORT_STRATEGY: $env:ORT_STRATEGY"
Write-Host "  ORT_LIB_LOCATION: $env:ORT_LIB_LOCATION"
Write-Host "  ONNX Runtime path: $onnxPath"
Write-Host ""

# 检查DLL是否存在
$dllPath = Join-Path $onnxPath "onnxruntime.dll"
if (Test-Path $dllPath) {
    $dllInfo = Get-Item $dllPath
    Write-Host "✅ Found onnxruntime.dll (1.22.1)" -ForegroundColor Green
    Write-Host "   Size: $($dllInfo.Length) bytes"
    Write-Host "   Modified: $($dllInfo.LastWriteTime)"
} else {
    Write-Host "❌ ERROR: onnxruntime.dll not found in $onnxPath" -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

# 确保target\debug目录存在
$targetDebugPath = "target\debug"
if (-not (Test-Path $targetDebugPath)) {
    Write-Host "Creating target\debug directory..." -ForegroundColor Yellow
    New-Item -ItemType Directory -Path $targetDebugPath -Force | Out-Null
}

# 复制正确版本的ONNX Runtime DLL到target\debug目录
Write-Host ""
Write-Host "Copying ONNX Runtime 1.22.1 DLLs to target\debug..." -ForegroundColor Yellow

$filesToCopy = @(
    "onnxruntime.dll",
    "onnxruntime_providers_shared.dll",
    "DirectML.dll"
)

foreach ($file in $filesToCopy) {
    $sourcePath = Join-Path $onnxPath $file
    $destPath = Join-Path $targetDebugPath $file
    
    if (Test-Path $sourcePath) {
        Copy-Item $sourcePath $destPath -Force
        Write-Host "  ✅ Copied $file" -ForegroundColor Green
    } else {
        Write-Host "  ⚠️  $file not found (optional)" -ForegroundColor Yellow
    }
}

# 构建项目
Write-Host ""
Write-Host "Building project..." -ForegroundColor Yellow
$buildResult = & cargo build --bin onnx_service
if ($LASTEXITCODE -ne 0) {
    Write-Host "❌ Build failed!" -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}
Write-Host "✅ Build completed successfully" -ForegroundColor Green

# 再次确保DLL文件存在（构建后可能被覆盖）
Write-Host ""
Write-Host "Ensuring correct DLL versions are in place..." -ForegroundColor Yellow
foreach ($file in $filesToCopy) {
    $sourcePath = Join-Path $onnxPath $file
    $destPath = Join-Path $targetDebugPath $file
    
    if (Test-Path $sourcePath) {
        Copy-Item $sourcePath $destPath -Force
    }
}
Write-Host "✅ Final DLL verification completed" -ForegroundColor Green

# 验证最终的DLL版本
$finalDllPath = Join-Path $targetDebugPath "onnxruntime.dll"
if (Test-Path $finalDllPath) {
    $finalDllInfo = Get-Item $finalDllPath
    Write-Host ""
    Write-Host "Final DLL verification:" -ForegroundColor Cyan
    Write-Host "  Path: $finalDllPath"
    Write-Host "  Size: $($finalDllInfo.Length) bytes (should be 12,416,032 for v1.22.1)"
    Write-Host "  Modified: $($finalDllInfo.LastWriteTime)"
}

# 启动服务
Write-Host ""
Write-Host "🚀 Starting ONNX Service..." -ForegroundColor Green
Write-Host "📖 Swagger UI will be available at: http://127.0.0.1:8080/swagger-ui/" -ForegroundColor Cyan
Write-Host ""

# 运行服务
& cargo run --bin onnx_service

Write-Host ""
Write-Host "Service stopped. Press Enter to exit..." -ForegroundColor Yellow
Read-Host
