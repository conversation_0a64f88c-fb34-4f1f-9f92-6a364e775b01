{"rustc": 16591470773350601817, "features": "[\"axum\"]", "declared_features": "[\"actix-web\", \"axum\", \"debug\", \"debug-embed\", \"rocket\"]", "target": 7413689917468982696, "profile": 2241668132362809309, "path": 9673742678261556867, "deps": [[2516681829802334884, "build_script_build", false, 390399783530972467], [4891297352905791595, "axum", false, 15221353620771618720], [8569119365930580996, "serde_json", false, 12091697258436455393], [9689903380558560274, "serde", false, 15283106852603414513], [9979094739671224239, "rust_embed", false, 9633974880835915126], [17303498555858728463, "u<PERSON><PERSON>a", false, 184483396908686156], [18071510856783138481, "mime_guess", false, 13746482764082197922]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\utoipa-swagger-ui-849507fe9b68acee\\dep-lib-utoipa_swagger_ui", "checksum": false}}], "rustflags": ["-L", "C:\\Users\\<USER>\\Desktop\\onnxruntime-win-x64-1.22.1\\lib", "-l", "onnxruntime"], "config": 2069994364910194474, "compile_kind": 0}