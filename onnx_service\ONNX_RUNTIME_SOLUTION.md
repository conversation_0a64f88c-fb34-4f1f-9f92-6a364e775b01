# ONNX Runtime API版本不匹配问题解决方案

## 问题描述

在使用ort库（版本2.0.0-rc.10）时遇到API版本不匹配的问题：
- ort库需要API版本22
- 系统中存在ONNX Runtime 1.17.1（API版本1-17）
- 错误信息：`The requested API version [22] is not available, only API versions [1, 17] are supported`

## 根本原因

系统中同时存在多个版本的ONNX Runtime DLL：
1. `C:\Windows\system32\onnxruntime.dll` (10,577,560 bytes) - 版本1.17.1
2. `C:\Users\<USER>\Desktop\onnxruntime-win-x64-1.22.1\lib\onnxruntime.dll` (12,416,032 bytes) - 版本1.22.1

Windows在加载DLL时优先使用系统PATH中的版本，导致应用程序使用了错误的版本。

## 解决方案

### 1. 配置文件设置

确保`.cargo/config.toml`正确配置：

```toml
[env]
# 设置ONNX Runtime库路径
ORT_STRATEGY = "system"
ORT_LIB_LOCATION = "C:\\Users\\<USER>\\Desktop\\onnxruntime-win-x64-1.22.1\\lib"

# 设置运行时DLL路径 - x64版本
[target.x86_64-pc-windows-msvc]
rustflags = [
    "-L", "C:\\Users\\<USER>\\Desktop\\onnxruntime-win-x64-1.22.1\\lib",
    "-l", "onnxruntime",
]
```

### 2. 依赖配置

确保`Cargo.toml`中使用正确的ort版本：

```toml
[dependencies]
# ONNX Runtime - 使用本地库而不是下载
ort = { version = "=2.0.0-rc.10", default-features = false, features = ["std"] }
```

### 3. DLL文件管理

关键解决步骤：将正确版本的DLL文件复制到应用程序的执行目录中：

```bash
# 复制主要DLL文件
Copy-Item "C:\Users\<USER>\Desktop\onnxruntime-win-x64-1.22.1\lib\onnxruntime.dll" -Destination ".\target\debug\" -Force
Copy-Item "C:\Users\<USER>\Desktop\onnxruntime-win-x64-1.22.1\lib\onnxruntime_providers_shared.dll" -Destination ".\target\debug\" -Force
```

### 4. 自动化脚本

#### PowerShell脚本（推荐）

使用`run_service.ps1`脚本，它会：
1. 设置正确的环境变量
2. 验证DLL文件存在
3. 复制正确版本的DLL到target/debug目录
4. 构建项目
5. 再次确保DLL版本正确
6. 启动服务

运行方式：
```powershell
powershell -ExecutionPolicy Bypass -File ".\run_service.ps1"
```

#### 批处理脚本

使用`run_service.bat`脚本作为替代方案。

### 5. 验证解决方案

运行测试程序验证配置：
```bash
cargo run --bin test_onnx_1_22_1
```

成功的输出应该显示：
- ✅ ort库基本检查通过
- ✅ ort库初始化测试通过
- ✅ 模型加载成功

## 文件版本验证

正确的ONNX Runtime 1.22.1 DLL文件特征：
- `onnxruntime.dll`: 12,416,032 bytes
- 修改日期: 2025/6/28 3:03:49

错误的ONNX Runtime 1.17.1 DLL文件特征：
- `onnxruntime.dll`: 10,577,560 bytes

## 启动服务

使用以下任一方式启动服务：

1. **PowerShell脚本（推荐）**：
   ```powershell
   .\run_service.ps1
   ```

2. **批处理脚本**：
   ```cmd
   .\run_service.bat
   ```

3. **手动方式**：
   ```bash
   # 确保DLL文件正确
   cargo build --bin onnx_service
   # 复制DLL文件到target/debug
   cargo run --bin onnx_service
   ```

## 服务访问

服务启动后可通过以下地址访问：
- 主服务：http://127.0.0.1:8080
- Swagger UI：http://127.0.0.1:8080/swagger-ui/

## 故障排除

如果仍然遇到API版本问题：

1. 检查target/debug目录中的DLL文件大小
2. 确保没有其他进程占用旧版本DLL
3. 重新运行PowerShell脚本
4. 检查系统PATH中是否有其他ONNX Runtime安装

## 总结

通过将正确版本的ONNX Runtime DLL文件复制到应用程序的执行目录，我们成功解决了API版本不匹配的问题。自动化脚本确保每次启动时都使用正确的DLL版本，避免了系统PATH中旧版本DLL的干扰。
