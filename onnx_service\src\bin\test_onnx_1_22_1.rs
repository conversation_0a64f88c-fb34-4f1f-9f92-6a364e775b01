use std::env;
use std::path::Path;

fn main() {
    // 加载.env文件
    dotenv::dotenv().ok();

    println!("=== ONNX Runtime 1.22.1 x64版本配置测试 ===");

    // 检查环境变量
    println!("\n1. 检查环境变量:");
    if let Ok(ort_lib) = env::var("ORT_LIB_LOCATION") {
        println!("   ORT_LIB_LOCATION: {}", ort_lib);
        let lib_path = Path::new(&ort_lib);
        println!("   库路径存在: {}", lib_path.exists());

        // 检查关键的库文件
        let onnxruntime_dll = lib_path.join("onnxruntime.dll");
        let onnxruntime_lib = lib_path.join("onnxruntime.lib");
        println!("   onnxruntime.dll 存在: {}", onnxruntime_dll.exists());
        println!("   onnxruntime.lib 存在: {}", onnxruntime_lib.exists());

        // 检查版本信息（如果有的话）
        if let Ok(entries) = std::fs::read_dir(&lib_path) {
            println!("   库目录内容:");
            for entry in entries {
                if let Ok(entry) = entry {
                    let file_name = entry.file_name();
                    if let Some(name) = file_name.to_str() {
                        if name.contains("onnx") {
                            println!("     - {}", name);
                        }
                    }
                }
            }
        }
    } else {
        println!("   ORT_LIB_LOCATION: 未设置");
    }

    if let Ok(ort_strategy) = env::var("ORT_STRATEGY") {
        println!("   ORT_STRATEGY: {}", ort_strategy);
    } else {
        println!("   ORT_STRATEGY: 未设置");
    }

    // 检查PATH中的DLL
    println!("\n2. 检查PATH中的ONNX Runtime DLL:");
    if let Ok(path_var) = env::var("PATH") {
        let paths: Vec<&str> = path_var.split(';').collect();
        let mut found_onnx = false;

        for path in paths {
            let dll_path = Path::new(path).join("onnxruntime.dll");
            if dll_path.exists() {
                println!("   找到 onnxruntime.dll 在: {}", path);
                found_onnx = true;
            }
        }

        if !found_onnx {
            println!("   ❌ 在PATH中未找到 onnxruntime.dll");
        }
    }

    // 检查模型文件
    println!("\n3. 检查模型文件:");
    let model_path = env::var("MODEL_PATH").unwrap_or_else(|_| "./models/model.onnx".to_string());
    let path = Path::new(&model_path);

    if path.exists() {
        println!("   ✅ 模型文件存在: {}", model_path);
        if let Ok(metadata) = path.metadata() {
            println!("   文件大小: {} bytes", metadata.len());
        }

        // 获取绝对路径
        if let Ok(absolute_path) = path.canonicalize() {
            println!("   绝对路径: {:?}", absolute_path);
        }
    } else {
        println!("   ❌ 模型文件不存在: {}", model_path);
    }

    // 尝试基本的ort初始化测试
    println!("\n4. 尝试ort库初始化:");
    match std::panic::catch_unwind(|| {
        println!("   正在测试ort库基本功能...");
        // 这里可以添加更多的ort库测试
        println!("   ✅ ort库基本检查通过");
    }) {
        Ok(_) => println!("   ✅ ort库初始化测试通过"),
        Err(_) => println!("   ❌ ort库初始化测试失败"),
    }

    println!("\n=== 测试完成 ===");
    println!("如果所有检查都通过，您的ONNX Runtime 1.22.1配置应该是正确的。");
}
