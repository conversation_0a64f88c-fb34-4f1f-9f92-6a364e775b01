@echo off
echo Starting ONNX Service...
echo Setting ONNX Runtime 1.22.1 path priority...

REM Add ONNX Runtime 1.22.1 path to the beginning of PATH
set "ONNX_PATH=C:\Users\<USER>\Desktop\onnxruntime-win-x64-1.22.1\lib"
set "PATH=%ONNX_PATH%;%PATH%"

echo ONNX Runtime path: %ONNX_PATH%
echo.

REM Check if DLL exists
if exist "%ONNX_PATH%\onnxruntime.dll" (
    echo Found onnxruntime.dll
) else (
    echo ERROR: onnxruntime.dll not found in %ONNX_PATH%
    pause
    exit /b 1
)

echo.
echo Starting service...
cargo run --bin onnx_service

pause
