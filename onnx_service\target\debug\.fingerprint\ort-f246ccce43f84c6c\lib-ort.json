{"rustc": 16591470773350601817, "features": "[\"ndarray\", \"std\"]", "declared_features": "[\"acl\", \"alternative-backend\", \"armnn\", \"azure\", \"cann\", \"copy-dylibs\", \"coreml\", \"cuda\", \"default\", \"directml\", \"download-binaries\", \"fetch-models\", \"half\", \"libloading\", \"load-dynamic\", \"migraphx\", \"ndarray\", \"nnapi\", \"num-complex\", \"nv\", \"onednn\", \"openvino\", \"qnn\", \"rknpu\", \"rocm\", \"std\", \"tensorrt\", \"tracing\", \"training\", \"tvm\", \"vitis\", \"webgpu\", \"xnnpack\"]", "target": 18116249120153087278, "profile": 2241668132362809309, "path": 868291305656337072, "deps": [[4455825158176820977, "ort_sys", false, 12780938909616305593], [18259966568667970611, "n<PERSON><PERSON>", false, 14221111239175477318], [18358627641082935174, "smallvec", false, 544596728564416140]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\ort-f246ccce43f84c6c\\dep-lib-ort", "checksum": false}}], "rustflags": ["-L", "C:\\Users\\<USER>\\Desktop\\onnxruntime-win-x64-1.22.1\\lib", "-l", "onnxruntime"], "config": 2069994364910194474, "compile_kind": 0}