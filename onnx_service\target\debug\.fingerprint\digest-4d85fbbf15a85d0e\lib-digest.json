{"rustc": 16591470773350601817, "features": "[\"alloc\", \"block-buffer\", \"core-api\", \"default\", \"std\"]", "declared_features": "[\"alloc\", \"blobby\", \"block-buffer\", \"const-oid\", \"core-api\", \"default\", \"dev\", \"mac\", \"oid\", \"rand_core\", \"std\", \"subtle\"]", "target": 7510122432137863311, "profile": 2225463790103693989, "path": 5379711370490419010, "deps": [[2352660017780662552, "crypto_common", false, 1106382495129231102], [10626340395483396037, "block_buffer", false, 17845855848937376256]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\digest-4d85fbbf15a85d0e\\dep-lib-digest", "checksum": false}}], "rustflags": ["-L", "C:\\Users\\<USER>\\Desktop\\onnxruntime-win-x64-1.22.1\\lib", "-l", "onnxruntime"], "config": 2069994364910194474, "compile_kind": 0}