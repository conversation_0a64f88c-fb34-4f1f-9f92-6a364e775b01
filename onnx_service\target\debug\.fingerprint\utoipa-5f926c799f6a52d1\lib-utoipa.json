{"rustc": 16591470773350601817, "features": "[\"axum_extras\", \"default\"]", "declared_features": "[\"actix_extras\", \"auto_into_responses\", \"axum_extras\", \"chrono\", \"debug\", \"decimal\", \"decimal_float\", \"default\", \"indexmap\", \"non_strict_integers\", \"openapi_extensions\", \"preserve_order\", \"preserve_path_order\", \"rc_schema\", \"repr\", \"rocket_extras\", \"serde_yaml\", \"smallvec\", \"time\", \"ulid\", \"url\", \"uuid\", \"yaml\"]", "target": 2151970672667237190, "profile": 2241668132362809309, "path": 6455506244609479545, "deps": [[6493259146304816786, "indexmap", false, 5128644413434902554], [8569119365930580996, "serde_json", false, 12091697258436455393], [9689903380558560274, "serde", false, 15283106852603414513], [14477260817627552954, "utoipa_gen", false, 3879566685449585700]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\utoipa-5f926c799f6a52d1\\dep-lib-utoipa", "checksum": false}}], "rustflags": ["-L", "C:\\Users\\<USER>\\Desktop\\onnxruntime-win-x64-1.22.1\\lib", "-l", "onnxruntime"], "config": 2069994364910194474, "compile_kind": 0}