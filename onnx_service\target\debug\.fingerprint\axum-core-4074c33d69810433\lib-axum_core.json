{"rustc": 16591470773350601817, "features": "[\"tracing\"]", "declared_features": "[\"__private_docs\", \"tracing\"]", "target": 2565713999752801252, "profile": 2241668132362809309, "path": 1504775876275435908, "deps": [[784494742817713399, "tower_service", false, 15258583568542421950], [1906322745568073236, "pin_project_lite", false, 10960016507308762011], [2517136641825875337, "sync_wrapper", false, 8810409436102507978], [7712452662827335977, "tower_layer", false, 7421703447326545080], [7858942147296547339, "rustversion", false, 5296070989764784322], [8606274917505247608, "tracing", false, 18191650935807564685], [9010263965687315507, "http", false, 888770377160779222], [10229185211513642314, "mime", false, 16824344108290317185], [10629569228670356391, "futures_util", false, 15286345949843558659], [11946729385090170470, "async_trait", false, 10605886362485534902], [14084095096285906100, "http_body", false, 3615934572663458495], [16066129441945555748, "bytes", false, 14526016353561065470], [16900715236047033623, "http_body_util", false, 5569380279369487524]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\axum-core-4074c33d69810433\\dep-lib-axum_core", "checksum": false}}], "rustflags": ["-L", "C:\\Users\\<USER>\\Desktop\\onnxruntime-win-x64-1.22.1\\lib", "-l", "onnxruntime"], "config": 2069994364910194474, "compile_kind": 0}