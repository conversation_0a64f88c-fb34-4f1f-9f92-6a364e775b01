@echo off
echo Starting ONNX Service with ONNX Runtime 1.22.1...
echo.

REM 设置环境变量
set ORT_STRATEGY=system
set ORT_LIB_LOCATION=C:\Users\<USER>\Desktop\onnxruntime-win-x64-1.22.1\lib

REM Add ONNX Runtime 1.22.1 path to the beginning of PATH
set "ONNX_PATH=C:\Users\<USER>\Desktop\onnxruntime-win-x64-1.22.1\lib"
set "PATH=%ONNX_PATH%;%PATH%"

echo Environment variables set:
echo ORT_STRATEGY=%ORT_STRATEGY%
echo ORT_LIB_LOCATION=%ORT_LIB_LOCATION%
echo ONNX Runtime path: %ONNX_PATH%
echo.

REM Check if DLL exists
if exist "%ONNX_PATH%\onnxruntime.dll" (
    echo ✅ Found onnxruntime.dll (1.22.1)
) else (
    echo ❌ ERROR: onnxruntime.dll not found in %ONNX_PATH%
    pause
    exit /b 1
)

REM 确保target\debug目录存在
if not exist "target\debug" (
    echo Creating target\debug directory...
    mkdir target\debug
)

REM 复制正确版本的ONNX Runtime DLL到target\debug目录
echo.
echo Copying ONNX Runtime 1.22.1 DLLs to target\debug...
copy /Y "%ONNX_PATH%\onnxruntime.dll" "target\debug\" >nul
copy /Y "%ONNX_PATH%\onnxruntime_providers_shared.dll" "target\debug\" >nul
copy /Y "%ONNX_PATH%\DirectML.dll" "target\debug\" 2>nul
echo ✅ DLL files copied successfully

REM 构建项目
echo.
echo Building project...
cargo build --bin onnx_service
if %ERRORLEVEL% neq 0 (
    echo ❌ Build failed!
    pause
    exit /b 1
)
echo ✅ Build completed successfully

REM 再次确保DLL文件存在（构建后可能被覆盖）
echo.
echo Ensuring correct DLL versions are in place...
copy /Y "%ONNX_PATH%\onnxruntime.dll" "target\debug\" >nul
copy /Y "%ONNX_PATH%\onnxruntime_providers_shared.dll" "target\debug\" >nul
copy /Y "%ONNX_PATH%\DirectML.dll" "target\debug\" 2>nul
echo ✅ Final DLL verification completed

REM 启动服务
echo.
echo 🚀 Starting ONNX Service...
echo 📖 Swagger UI will be available at: http://127.0.0.1:8080/swagger-ui/
echo.
cargo run --bin onnx_service

pause
